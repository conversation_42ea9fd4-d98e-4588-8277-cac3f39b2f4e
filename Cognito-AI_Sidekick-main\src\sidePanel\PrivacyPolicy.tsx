import { cn } from 'src/background/util';
import { But<PERSON> } from '@/components/ui/button';
import { FiX } from 'react-icons/fi';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface PrivacyPolicyProps {
  onClose?: () => void;
}

export const PrivacyPolicy = ({ onClose }: PrivacyPolicyProps = {}) => {
  return (
    <div
      id="privacy-policy"
      className="relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden"
    >
      {/* Close button in top-right corner */}
      {onClose && (
        <div className="absolute top-4 right-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                onClick={onClose}
                aria-label="Close Privacy Policy"
              >
                <FiX size="18px" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
              Close Privacy Policy
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-apple-title2 font-semibold text-foreground mb-4">
            Privacy Policy
          </h1>
          <p className="text-apple-body text-muted-foreground">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </div>

        <div className="space-y-8">
          {/* Privacy-First Approach */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Privacy-First Design
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Chromepanion is designed with your privacy as the top priority. All AI processing 
                happens locally on your device using Ollama, ensuring your conversations and data 
                never leave your computer.
              </p>
              <p>
                We believe in transparent, local AI assistance that respects your privacy and 
                gives you complete control over your data.
              </p>
            </div>
          </section>

          {/* Data Collection */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              What Data We Collect
            </h2>
            <div className="space-y-4 text-apple-body text-foreground">
              <div>
                <h3 className="text-apple-callout font-medium text-foreground mb-2">
                  Local Storage Only
                </h3>
                <ul className="space-y-2 text-muted-foreground ml-4">
                  <li>• Chat history and conversations (stored locally in your browser)</li>
                  <li>• AI model preferences and settings</li>
                  <li>• Persona selections and configurations</li>
                  <li>• Theme and interface preferences</li>
                </ul>
              </div>
              <div>
                <h3 className="text-apple-callout font-medium text-foreground mb-2">
                  No Remote Data Collection
                </h3>
                <p className="text-muted-foreground">
                  Chromepanion does not collect, store, or transmit any personal data to external 
                  servers. All your information remains on your device.
                </p>
              </div>
            </div>
          </section>

          {/* Google Search Integration */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Google Search Integration
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                When you use web search features, Chromepanion fetches search results from Google. 
                This interaction is subject to Google's privacy policy and terms of service.
              </p>
              <p className="text-muted-foreground">
                Search queries are sent directly to Google's servers, not through our systems. 
                The search results are then processed locally by your Ollama AI models.
              </p>
            </div>
          </section>

          {/* Local AI Processing */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Local AI Processing
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                All AI conversations and analysis happen entirely on your device through Ollama. 
                Your messages, search results, and AI responses are processed locally without 
                any external API calls.
              </p>
              <p className="text-muted-foreground">
                This ensures complete privacy and allows you to use Chromepanion even without 
                an internet connection (except for web search features).
              </p>
            </div>
          </section>

          {/* Data Security */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Data Security
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Your data is stored locally using your browser's secure storage mechanisms. 
                Chat history and settings are encrypted and stored in your browser's local storage.
              </p>
              <p className="text-muted-foreground">
                You can delete all stored data at any time through the settings page or by 
                clearing your browser's extension data.
              </p>
            </div>
          </section>

          {/* Your Rights */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Your Rights and Control
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Since all data is stored locally on your device, you have complete control:
              </p>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li>• View and export your chat history at any time</li>
                <li>• Delete individual conversations or all data</li>
                <li>• Modify or update your preferences</li>
                <li>• Uninstall the extension to remove all data</li>
              </ul>
            </div>
          </section>

          {/* Contact */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Questions or Concerns
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                If you have any questions about this privacy policy or how Chromepanion handles 
                your data, please feel free to reach out through our support channels.
              </p>
              <p className="text-muted-foreground">
                Remember: Your privacy is our priority, and all processing happens locally on 
                your device.
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
