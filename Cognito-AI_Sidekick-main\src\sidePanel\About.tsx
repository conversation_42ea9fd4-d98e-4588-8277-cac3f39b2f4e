import { cn } from '@/src/background/util';
import { But<PERSON> } from '@/components/ui/button';
import { FiX } from 'react-icons/fi';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

interface AboutProps {
  onClose?: () => void;
}

export const About = ({ onClose }: AboutProps = {}) => {
  return (
    <div className="relative z-[1] top-0 w-full h-full flex flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground scrollbar-hidden about-page">
      {/* Close button in top-right corner */}
      {onClose && (
        <div className="absolute top-4 right-4 z-10">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center"
                onClick={onClose}
                aria-label="Close About"
              >
                <FiX size="18px" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" className="bg-popover text-popover-foreground border border-border">
              Close About
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <div className="w-full max-w-2xl mx-auto px-6 pt-6 pb-10">
        <div className="mb-8">
          <h1 className="text-apple-title1 font-bold text-foreground mb-2">
            About Chromepanion
          </h1>
          <p className="text-apple-body text-muted-foreground">
            Your privacy-first local AI web search assistant
          </p>
        </div>

        <div className="space-y-8">
          {/* App Information */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Application Details
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Name:</span>
                <span className="font-medium">Chromepanion</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Version:</span>
                <span className="font-medium">1.0.0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Type:</span>
                <span className="font-medium">Browser Extension</span>
              </div>
            </div>
          </section>

          {/* Description */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              What is Chromepanion?
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Chromepanion is a privacy-first local AI web search assistant that transforms 
                how you interact with information online. By combining the power of Google search 
                with local AI processing through Ollama, it provides intelligent, contextual 
                responses while keeping your data completely private.
              </p>
              <p>
                Unlike traditional AI assistants that send your data to external servers, 
                Chromepanion processes everything locally on your device, ensuring your 
                conversations and search queries remain private and secure.
              </p>
            </div>
          </section>

          {/* Key Features */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Key Features
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Local AI Processing:</strong> All AI conversations happen on your device using Ollama</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Google Search Integration:</strong> Seamlessly search the web and get AI-powered insights</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Multiple Personas:</strong> Choose from 10 specialized AI personas for different use cases</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Chat History:</strong> Save and revisit your conversations with automatic title generation</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Theme Support:</strong> Light and dark mode themes for comfortable use</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-primary mt-1">•</span>
                  <span><strong className="text-foreground">Export Options:</strong> Save conversations in multiple formats (Markdown, JSON, Text, Image)</span>
                </li>
              </ul>
            </div>
          </section>

          {/* Privacy Statement */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Privacy-First Design
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Your privacy is our top priority. Chromepanion is designed to keep your data 
                local and secure:
              </p>
              <ul className="space-y-2 text-muted-foreground ml-4">
                <li>• All AI processing happens locally on your device</li>
                <li>• No data is sent to external AI services</li>
                <li>• Chat history is stored locally in your browser</li>
                <li>• You have complete control over your data</li>
                <li>• No tracking or analytics</li>
              </ul>
            </div>
          </section>

          {/* Technical Details */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Technical Information
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">AI Engine:</span>
                  <span className="font-medium">Ollama (Local)</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Search Provider:</span>
                  <span className="font-medium">Google Search</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Data Storage:</span>
                  <span className="font-medium">Local Browser Storage</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Platform:</span>
                  <span className="font-medium">Chrome Extension (Manifest V3)</span>
                </div>
              </div>
              <p className="text-muted-foreground text-apple-footnote mt-4">
                Requires Ollama to be installed and running locally for AI functionality.
              </p>
            </div>
          </section>

          {/* Support Section */}
          <section className={cn(
            "p-6 rounded-xl",
            "bg-card border border-border shadow-sm"
          )}>
            <h2 className="text-apple-title3 font-semibold text-foreground mb-4">
              Support Development
            </h2>
            <div className="space-y-3 text-apple-body text-foreground">
              <p>
                Chromepanion is developed with passion to provide you with the best privacy-first
                AI experience. If you find it useful, consider supporting its continued development.
              </p>
              <div className="flex justify-center mt-4">
                <a href='https://ko-fi.com/T6T11G2CYS' target='_blank' rel="noopener noreferrer">
                  <img
                    height='36'
                    style={{border: '0px', height: '36px'}}
                    src='https://storage.ko-fi.com/cdn/kofi6.png?v=6'
                    alt='Buy Me a Coffee at ko-fi.com'
                  />
                </a>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
