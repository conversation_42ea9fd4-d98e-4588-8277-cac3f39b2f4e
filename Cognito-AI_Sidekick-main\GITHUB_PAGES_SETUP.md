# GitHub Pages Setup for Chromepanion Privacy Policy

This guide will help you set up GitHub Pages to host the privacy policy page for Chromepanion.

## Files Created

- `privacy-policy.html` - Standalone HTML version of the privacy policy page
- This README file with setup instructions

## Setting Up GitHub Pages

### Option 1: Using GitHub Repository Settings (Recommended)

1. **Push the files to your GitHub repository:**
   ```bash
   git add privacy-policy.html GITHUB_PAGES_SETUP.md
   git commit -m "Add privacy policy HTML page for GitHub Pages"
   git push origin main
   ```

2. **Enable GitHub Pages:**
   - Go to your repository on GitHub
   - Click on "Settings" tab
   - Scroll down to "Pages" section in the left sidebar
   - Under "Source", select "Deploy from a branch"
   - Choose "main" branch and "/ (root)" folder
   - Click "Save"

3. **Access your privacy policy:**
   - Your privacy policy will be available at: `https://yourusername.github.io/your-repo-name/privacy-policy.html`
   - GitHub will provide the exact URL in the Pages settings

### Option 2: Using GitHub Actions (Advanced)

If you want more control over the deployment process, you can use GitHub Actions:

1. Create `.github/workflows/pages.yml`:
   ```yaml
   name: Deploy to GitHub Pages
   
   on:
     push:
       branches: [ main ]
     workflow_dispatch:
   
   permissions:
     contents: read
     pages: write
     id-token: write
   
   concurrency:
     group: "pages"
     cancel-in-progress: false
   
   jobs:
     deploy:
       environment:
         name: github-pages
         url: ${{ steps.deployment.outputs.page_url }}
       runs-on: ubuntu-latest
       steps:
         - name: Checkout
           uses: actions/checkout@v4
         - name: Setup Pages
           uses: actions/configure-pages@v4
         - name: Upload artifact
           uses: actions/upload-pages-artifact@v3
           with:
             path: '.'
         - name: Deploy to GitHub Pages
           id: deployment
           uses: actions/deploy-pages@v4
   ```

## Features of the HTML Privacy Policy

### Design Features
- **Apple Design Language**: Matches the extension's visual style
- **Responsive Design**: Works on desktop and mobile devices
- **Dark/Light Theme**: Automatic theme detection with manual toggle
- **Clean Typography**: Uses Apple's typography scale
- **Card-based Layout**: Organized sections with subtle shadows

### Technical Features
- **Standalone HTML**: No external dependencies
- **CSS Variables**: Easy theme customization
- **Local Storage**: Remembers user's theme preference
- **Semantic HTML**: Accessible and SEO-friendly
- **Progressive Enhancement**: Works without JavaScript

### Content Sections
1. **Privacy-First Design** - Emphasizes local AI processing
2. **Data Collection** - Details what data is stored locally
3. **Google Search Integration** - Transparent about search data
4. **Local AI Processing** - Explains Ollama's role
5. **Data Security** - Local storage and encryption
6. **User Rights** - Complete data control options
7. **Contact Information** - Support and privacy commitment

## Customization

### Updating Content
Edit the `privacy-policy.html` file to modify:
- Company information
- Contact details
- Policy sections
- Last updated date (automatically set via JavaScript)

### Styling Changes
Modify the CSS variables in the `<style>` section:
```css
:root {
    --background: #ffffff;
    --foreground: #1d1d1f;
    --card: #ffffff;
    --border: #d1d1d6;
    /* Add more customizations */
}
```

### Adding Analytics (Optional)
Add Google Analytics or other tracking code before the closing `</head>` tag:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

## Integration with Extension

### Linking from Extension
Update your extension's privacy menu item to link to the hosted page:
```javascript
// In your extension code
const privacyPolicyUrl = 'https://yourusername.github.io/your-repo-name/privacy-policy.html';

// Open in new tab
chrome.tabs.create({ url: privacyPolicyUrl });
```

### Embedding in Extension
You can also embed the content directly in your extension by:
1. Copying the HTML content
2. Converting it to a React/TypeScript component
3. Using the same CSS variables for consistency

## Maintenance

### Regular Updates
- Review and update the privacy policy content regularly
- Update the "Last updated" date when making changes
- Test the page on different devices and browsers
- Monitor GitHub Pages deployment status

### Version Control
- Keep the HTML file in sync with your extension's privacy policy
- Use git tags to track privacy policy versions
- Document changes in commit messages

## Troubleshooting

### Common Issues
1. **Page not loading**: Check GitHub Pages settings and deployment status
2. **Styling issues**: Verify CSS variables and responsive design
3. **Theme not working**: Check JavaScript console for errors
4. **Content not updating**: Clear browser cache or check deployment

### Support
If you encounter issues with GitHub Pages setup:
- Check GitHub's [Pages documentation](https://docs.github.com/en/pages)
- Review deployment logs in the Actions tab
- Ensure your repository is public (for free GitHub Pages)

## Example URLs

After setup, your privacy policy will be accessible at:
- `https://yourusername.github.io/chromepanion/privacy-policy.html`
- Custom domain: `https://your-domain.com/privacy-policy.html` (if configured)

Replace `yourusername` and `chromepanion` with your actual GitHub username and repository name.
